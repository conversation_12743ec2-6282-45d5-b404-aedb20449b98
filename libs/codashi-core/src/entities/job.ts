import { isoDateParser } from '@awe/core';
import { z } from 'zod';

const locationSchema = z.object({
  address: z
    .string()
    .nullable()
    .describe(
      'To add multiple address lines, use \n. For example, 1234 Glücklichkeit Straße\nHinterhaus 5. Etage li.'
    ),
  postal_code: z.string().nullable(),
  city: z.string().nullable(),
  country_code: z
    .string()
    .describe('code as per ISO-3166-1 ALPHA-2, e.g. US, AU, IN'),
  region: z
    .string()
    .nullable()
    .describe(
      'The general region where you live. Can be a US state, or a province, for instance.'
    ),
});

const skillSchema = z.object({
  name: z.string().describe('e.g. Web Development'),
  level: z.string().nullable().describe('e.g. Master'),
  keywords: z
    .array(z.string().describe('e.g. HTML'))
    .nullable()
    .describe('List some keywords pertaining to this skill'),
});

const metaSchema = z.object({
  canonical: z
    .string()
    .url()
    .nullable()
    .describe('URL (as per RFC 3986) to latest version of this document'),
  version: z
    .string()
    .nullable()
    .describe('A version field which follows semver - e.g. v1.0.0'),
  last_modified: z
    .string()
    .nullable()
    .describe('Using ISO 8601 with YYYY-MM-DDThh:mm:ss'),
});

// Define the main job schema - follows the JSON job schema standard format
// https://github.com/jsonresume/resume-schema/blob/master/job-schema.json
const jsonJobSchema = z.object({
  title: z.string().describe('The title of the job, e.g. Web Developer'),
  company: z.string().describe('The name of the company, e.g. Microsoft'),
  type: z
    .string()
    .describe('The type of the job, e.g. Full-time, part-time, contract, etc.'),
  date: isoDateParser.nullable().describe('When the job was posted'),
  description: z.string().describe('A description of the job'),
  location: locationSchema
    .nullable()
    .describe('Where the actual work will be performed. Can be null if remote'),
  remote: z
    .enum(['Full', 'Hybrid', 'None'])
    .nullable()
    .describe('the level of remote work available'),
  salary: z
    .string()
    .nullable()
    .describe(
      'A number, for example - 100000$, or a range like $100000-200000'
    ),
  experience: z
    .string()
    .nullable()
    .describe(
      'The experience level required for the job, e.g. Senior or Junior or Middle'
    ),
  responsibilities: z
    .array(
      z
        .string()
        .describe(
          'The responsibilities of the job, e.g. Build out a new API for our customer base.'
        )
    )
    .nullable()
    .describe('The responsibilities of the job'),
  qualifications: z
    .array(
      z
        .string()
        .describe(
          'Qualifications required for the job, e.g. undergraduate degree, etc.'
        )
    )
    .nullable()
    .describe('Qualifications required for the job'),
  skills: z
    .array(skillSchema)
    .nullable()
    .describe('The skills required for the job, e.g. JavaScript, React, etc.'),
  meta: metaSchema
    .nullable()
    .describe(
      'The schema version and any other tooling configuration lives here'
    ),
});

const extendedFields = z.object({
  experience: z
    .array(jsonJobSchema.shape.experience)
    .nullable()
    .default(null)
    .describe(
      'The experience levels required for the job, can be suitable for multiple levels'
    ),
  // to be inferred or searched via AI
  company_meta: z.object({
    type: z
      .string()
      .nullable()
      .describe('The type of the company, e.g. Startup, Mid-size, Large'),
    size: z
      .string()
      .nullable()
      .describe('The size of the company, e.g. 1-10, 11-100, 101-1000, etc.'),
    tone: z
      .string()
      .nullable()
      .describe('The tone of the company, e.g. Formal, Informal, etc.'),
    internal_company_id: z
      .string()
      .brand('internal_company_id')
      .nullable()
      .describe(
        'Codashi internal company id. We use this to link to the company profile with useful information for the candidate'
      ),
  }),
  notes: z
    .array(z.string())
    .nullable()
    .default(null)
    .describe(
      'Notes about the job, made by the candidate for the sake of the AI adjusting the resume'
    ),
});

// superset of the json job schema - our software's assumptions on what
// deserves to be done deeper
const extendedJobSchema = jsonJobSchema.merge(extendedFields);

export type Job = z.infer<typeof extendedJobSchema>;
